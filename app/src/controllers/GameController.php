<?php

declare( strict_types=1 );


namespace App\controllers;


use App\helpers\yiisoft\Json;
use App\models\GamePlayModel;
use App\services\DBService;
use App\services\GameService;
use App\services\LocaleService;
use App\services\ProjectService;
use App\services\RegionService;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Exception\HttpNotFoundException;
use Throwable;

class GameController extends AbstractController {

    /** @var GameService */
    public $service;

    /** @var ProjectService */
    public $project;

    /** @var LocaleService */
    public $localeService;

    /** @var RegionService */
    public $regionService;

    public function __construct( GameService $service = null, ProjectService $projectService = null, LocaleService $localeService = null, RegionService $regionService = null ) {
        $this->service       = $service ?? GameService::instance();
        $this->project       = $projectService ?? ProjectService::instance();
        $this->localeService = $localeService ?? LocaleService::instance();
        $this->regionService = $regionService ?? RegionService::instance();
        parent::__construct();
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'name' => 'fu-fish', 'gameId' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pageGame( Request $request, Response $response, array $args = [] ): Response {

        return $this->project->getRenderer()->renderHtml( $response, $this->service::buildGamePage( $args['gameId'] ) );
    }


    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'languageCode' => 'en', 'gameCode' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function generatePlayGameUrl( Request $request, Response $response, array $args = [] ): Response {
        $languageCode = $args['languageCode'];
        $regionCode   = $args['regionCode'];
        $gameCode     = $args['gameCode'];

        $project     = ProjectService::instance();
        $gameService = GameService::instance();

        $games = $gameService->getProviderGames( $regionCode );

        if ( isset( $games[ $gameCode ] ) ) {
            $response->getBody()->write( 'https://' . ProjectService::generatePlayGameUrl( $gameCode, $languageCode, $regionCode ) );

            return $response;

        }
        throw new HttpNotFoundException( $project->getRequest() );

    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'languageCode' => 'en', 'gameCode' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pagePlay( Request $request, Response $response, array $args = [] ): Response {
        /** @var GameService $gameService */
        $gameService = GameService::instance();

        /** @var LocaleService $localeService */
        $localeService = LocaleService::instance();

        /** @var RegionService $regionService */
        $regionService = RegionService::instance();

        $languages = $localeService->getAvailableLanguages();

        /** !IMPORTANT    should be $args['gameData']   */
        $gameData = $gameService->decodeGameData( $args['gameData'] );
        $htmlString = '';
        if ( $gameData instanceof GamePlayModel ) {

            $regionService->setCurrentRegionCode( $gameData->regionCode );
            $localeService->setCurrentLanguageCode( $gameData->languageCode );

            $htmlString = $gameService->runGame(
                $gameData->gameId,
                'fun',
                $languages[ $gameData->languageCode ]['boxFolderName'],
                $request,
                $response
            );
        }
        $response->getBody()->write( $htmlString );

        return $response;
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'gameCode' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function marketingKitJson( Request $request, Response $response, array $args = [] ): Response {

        $gameInfo = [ 'titleEN' => 'unknown', 'marketingKit' => [] ];
        try {
            $dbService = DBService::instance();

            $gameKit = $dbService->getMarketingKit( $args['gameCode'] );
            if ( is_array( $gameKit ) && isset( $gameKit['data'] ) ) {
                $gameMKData = Json::decode( $gameKit['data'], true );
                if ( is_array( $gameMKData ) && isset( $gameMKData['marketingKit'] ) && is_array( $gameMKData['marketingKit'] ) ) {
                    foreach ( $gameMKData['marketingKit'] as $folderName => $files ) {
                        if ( is_string( $folderName ) && is_array( $files ) ) {
                            foreach ( $files as $i => $filePath ) {
                                $gameMKData['marketingKit'][ $folderName ][ $i ] =
                                    'https://storage.googleapis.com/lobby.stg1.m27613.com/gamestudios/' . $args['gameCode'] . '/' . $filePath;
                            }
                        }
                    }
                }
                $gameInfo = $gameMKData;
            }

        } catch ( \Exception $exception ) {

        }

        $response->getBody()->write( Json::encode( $gameInfo ) );

        return $response
            ->withHeader( 'Content-Type', 'application/json' );
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'languageCode' => 'en', 'regionCode' => 'eu' 'gameCode' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pagePlayIframe( Request $request, Response $response, array $args = [] ): Response {

        /** @var GameService $gameService */
        $gameService = GameService::instance();

        /** @var LocaleService $localeService */
        $localeService = LocaleService::instance();

        /** @var RegionService $regionService */
        $regionService = RegionService::instance();

        $languages = $localeService->getAvailableLanguages();

        $regionService->setCurrentRegionCode( $args['regionCode'] );
        $localeService->setCurrentLanguageCode( $args['languageCode'] );

        $gamePlayUrl  = $gameService->runGame(
            $args['gameCode'],
            'fun',
            $languages[ $args['languageCode'] ]['boxFolderName'],
            $request,
            $response,
            true
        );
        $languageCode = $args['languageCode'];
        $response->getBody()->write(
            <<<HTML
<!DOCTYPE html>
<html lang="{$languageCode}"> 
<head>
<meta id="viewport" name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1, minimum-scale=1">
<style type="text/css">
body {
    margin: 0;
    overflow: hidden;
}
#iframe1 {
    position:absolute;
    left: 0;
    width: 100%;
    top: 0;
    height: 100%;
}
</style>
<title></title>
</head>
<body>
<iframe id="iframe1" src="{$gamePlayUrl}" frameborder="0"></iframe>
</body>
</html>
HTML
        );

        return $response;
    }


    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'name' => 'world-hallowing', 'playlistCode' => 'sw_fufish', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pagePlaylist( Request $request, Response $response, array $args = [] ): Response {

        return $this->project->getRenderer()->renderHtml( $response, $this->service::buildPlaylistPage( $args['playlistCode'] ) );
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'categoryCode' => 'top', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pageGames( Request $request, Response $response, array $args = [] ): Response {

        $categoryCode        = null;
        $defaultCategoryCode = null;
        $gameCategories      = GameService::getGameCategories( $this->regionService->getCurrentRegionCode(), $this->localeService->getCurrentLanguageCode() );
        foreach ( $gameCategories->getId() as $category ) {
            if ( $defaultCategoryCode === null ) {
                $defaultCategoryCode = $category->categoryCode->current;
            }
//            if ( isset( $args['categoryCode'] ) && $category->categoryCode->current === $args['categoryCode'] ) {
//                $categoryCode = $args['categoryCode'];
//                break;
//            }
        }
        if ( $categoryCode === null ) {
            $categoryCode = $defaultCategoryCode;
        }

        return $this->project->getRenderer()->renderHtml( $response, $this->service::buildGamesPage( (string) $categoryCode ) );
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param $args = [ 'categoryCode' => 'top', ]
     *
     * @return Response
     * @throws Throwable
     */
    public function pageGames2( Request $request, Response $response, array $args = [] ): Response {

        return $this->project->getRenderer()->renderHtml( $response, $this->service::buildGamesPage2() );
    }

    /**
     * @param Request $request
     * @param Response $response
     * @param array $args
     *
     * @return Response
     * @throws Throwable
     */
    public function pageLiveCasino( Request $request, Response $response, array $args = [] ): Response {

        return $this->project->getRenderer()->renderHtml( $response, $this->service::buildLiveCasinoPage() );
    }

}