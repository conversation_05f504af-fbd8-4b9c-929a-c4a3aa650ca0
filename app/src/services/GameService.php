<?php

declare( strict_types=1 );


namespace App\services;

use App\helpers\yiisoft\Json;
use App\models\Crypto;
use App\models\enums\EnumByParam;
use App\models\enums\EnumCdn;
use App\models\enums\EnumProjectEnv;
use App\models\enums\EnumRegion;
use App\models\GamePlayModel;
use App\models\ItemModel;
use App\models\sanity\documents\Game;
use App\models\sanity\documents\GameCategory;
use App\models\sanity\documents\GameCode;
use App\models\sanity\documents\GamePlaylist;
use App\models\sanity\documents\PageGames;
use App\models\sanity\documents\PageLiveCasino;
use App\models\sanity\enum\GameCategoryTypes;
use App\models\sanity\objects\GameLine;
use App\models\sanity\SanityReference;
use App\models\ServiceModel;
use App\models\SiteCredentials;
use App\models\views\ViewPageGameModel;
use App\models\views\ViewPageGames2Model;
use App\models\views\ViewPageGamesModel;
use App\models\views\ViewPageLiveCasino;
use App\models\views\ViewPagePlaylistModel;
use App\services\Interfaces\InterfaceGameService;
use App\traits\Singleton;
use Exception;
use Google\Cloud\Storage\StorageObject;
use GpApi\Config;
use GpApi\Customer;
use GpApi\models\GameInfoModel;
use GpApi\models\GameURLInfoModel;
use GpApi\Site;
use \JsonException;
use Monolog\Logger;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use RuntimeException;
use Slim\Exception\HttpNotFoundException;
use Throwable;

/**
 * Class GameService
 * @package App\services
 * @method static self instance()
 */
class GameService extends ServiceModel implements InterfaceGameService {

    use Singleton;

    protected $lifeTime = 600; // in seconds

    /** @var ItemModel $games */
    protected $games;

    /** @var ItemModel $gameCategories */
    protected $gameCategories;

    protected $gamesPerLayout = [ 'standard' => 1, 'large' => 2 ];

    /** @var string */
    protected $sessionLanguageParam = 'gamesLanguageCode';

    /** @var string */
    protected $currentLanguageCode;

    /** @var string */
    protected $defaultLanguageCode;


    public static function init(): void {

        $project = ProjectService::instance();

        $self           = self::instance();
        $self->lifeTime = 24 * 60 * 60;

        $self->setLogger( LoggerService::getLogger( PROJECT_NAME . '-game-service' ) );

        try {

            $request     = $project->getRequest();
            $queryParams = $request->getQueryParams();

            if ( isset( $queryParams['game-language'] ) && $self->isLanguageCodeExists( $queryParams['game-language'] ) ) {
                $regionCode = $queryParams['game-language'];
            } else {
                $regionCode = $self->getCurrentLanguageCode();
            }

            $self->setCurrentLanguageCode( $regionCode );
        } catch ( Exception $e ) {
            $self->getLogger()->error( $e );
        }

    }

    /**
     * @param string $url
     * @param string $language
     *
     * @return string
     */
    private function replaceLanguageParam( $url, $language ): string {

        $parsedUrlArray = parse_url( $url );

        $queryParams = [];
        parse_str( $parsedUrlArray['query'], $queryParams );

        if ( array_key_exists( 'language', $queryParams ) ) {
            unset( $queryParams['language'] );
        }

        $newQueryParams = http_build_query( $queryParams );

        $parsedUrlArray['scheme'] .= '://';
        $parsedUrlArray['path']   .= '?';
        $parsedUrlArray['query']  = $newQueryParams;

        $newUrl = implode( $parsedUrlArray );

        return $newUrl . '&language=' . $language;
    }

    /**
     * @param string $url
     * @param string $language
     *
     * @return string
     */
    private function replaceCurrencyParam( $url, $currency ): string {

        $parsedUrlArray = parse_url( $url );

        $queryParams = [];
        parse_str( $parsedUrlArray['query'], $queryParams );

        if ( array_key_exists( 'currency', $queryParams ) ) {
            unset( $queryParams['currency'] );
        }

        $newQueryParams = http_build_query( $queryParams );

        $parsedUrlArray['scheme'] .= '://';
        $parsedUrlArray['path']   .= '?';
        $parsedUrlArray['query']  = $newQueryParams;

        $newUrl = implode( $parsedUrlArray );

        return $newUrl . '&currency=' . $currency;
    }

    /**
     * @param string $url
     * @param string $language
     *
     * @return string
     */
    private function removeToken( $url, $token = "startGameToken" ): string {

        $parsedUrlArray = parse_url( $url );

        $queryParams = [];
        parse_str( $parsedUrlArray['query'], $queryParams );

        if ( array_key_exists( $token, $queryParams ) ) {
            unset( $queryParams[ $token ] );
        }

        $newQueryParams = http_build_query( $queryParams );

        $parsedUrlArray['scheme'] .= '://';
        $parsedUrlArray['path']   .= '?';
        $parsedUrlArray['query']  = $newQueryParams;

        $newUrl = implode( $parsedUrlArray );

        return $newUrl;
    }

    /**
     * @param string $gameURLInfoModel
     * @param string $language
     * @param string $currency
     *
     * @return string
     */
    public function getGameUrl( $gameURLInfoModel, $language, $currency = null, $removeToken = false ): string {

        $self = self::instance();

        $part1 = $gameURLInfoModel;

        if ( ! empty( $language ) ) {

            $newGameUrl = $self->replaceLanguageParam( $gameURLInfoModel, $language );
            if ( $currency ) {
                $newGameUrl = $self->replaceCurrencyParam( $newGameUrl, $currency );
            }
            if ( $removeToken === true ) {
                $newGameUrl = $self->removeToken( $newGameUrl );
            }
            $part1 = $newGameUrl;
        }

        return $part1;

    }

    public function userProvider( string $currentRegionCode, SiteCredentials $credentials ): void {
        Config::setDebug( true );
        Config::setAuthorizationFunction( static function () use ( $currentRegionCode, $credentials ) {

            if ( $credentials->checkEntryPoint() ) {
                Site::setToken( $credentials->token );

                Site::setApiUrl( $credentials->entryPointSite );

                Customer::setApiUrl( $credentials->entryPointPlayer );

                if ( Site::validateToken( true ) ) {

                    return true;
                }
            }

            return false;
        } );

    }

    /**
     * @param string|null $regionCode
     *
     * @return array|null
     */
    public function getJackpotGames( string $regionCode = null ): ?array {

        $self = self::instance();

        $dbService = DBService::instance();

        $regionService = RegionService::instance();

        $jackpotGames = [];

        $currentRegionCode = ( is_string( $regionCode ) && $regionService->isRegionCodeExists( $regionCode )
            ? $regionCode
            : $regionService->getCurrentRegionCode() );

        try {
            $games = $dbService::getJackpotGames( $currentRegionCode );
            if ( is_array( $games ) ) {
                foreach ( $games as $gameRow ) {
                    $game                          = Json::decode( $gameRow['game_data'], true );
                    $jackpotGames[ $game['code'] ] = $game;
                }
            }

            return $jackpotGames;
        } catch ( Exception $exception ) {
            $self->getLogger()->error( (string) $exception );

            return null;
        }
    }

    public function getProviderGames( string $regionCode = null, int $limit = 1000, string $gameCode = null ): ?array {
        $self = self::instance();

        $regionService = RegionService::instance();
        $project       = ProjectService::instance();

        $projectConfig = $project::getConfig();

        $currentRegionCode = ( is_string( $regionCode ) && $regionService->isRegionCodeExists( $regionCode )
            ? $regionCode
            : $regionService->getCurrentRegionCode() );

        $entities = $projectConfig['regions']['available'][ $currentRegionCode ]['entities'];

        $activeGames = [];

        foreach ( $entities as $entity ) {

            $credentials = new SiteCredentials(
                $entity['site-api']['url'],
                $entity['player-api']['url'],
                $entity['token']['value']
            );

            $self->userProvider( $currentRegionCode, $credentials );

            set_time_limit( $self->lifeTime );

            $params = [
                'limit' => $limit
            ];
            if ( $gameCode ) {
                $params['code'] = $gameCode;
            }
            $games = Site::getGames( $params );


            if ( is_array( $games ) ) {

                /** @var GameInfoModel $game */
                foreach ( $games as $game ) {
                    if ( is_object( $game ) ) {
                        if ( property_exists( $game, 'limits' ) ) {
                            unset( $game->limits );
                        }
                        $isJackpot = false;
                        if ( ( property_exists( $game, 'settings' ) && is_object( $game->settings ) &&
                               property_exists( $game->settings, 'jackpotId' ) &&
                               is_object( $game->settings->jackpotId ) ) ) {
                            foreach ( $game->settings->jackpotId as $key => $value ) {
                                if ( substr( $value, - 5 ) === '_test' ) {
                                    $game->settings->jackpotId->$key = substr( $value, 0, - 5 );
                                }
                            }
                            $isJackpot = true;
                        }

                        $activeGames[ $game->code ] = [
                            'game_code'   => $game->code,
                            'title'       => $game->title,
                            'releaseDate' => $game->releaseDate,
                            'provider'    => $currentRegionCode,
                            'is_active'   => ( property_exists( $game, 'status' ) && $game->status === 'normal' ),
                            'is_jackpot'  => $isJackpot,
                            /**
                             * GRC Games should be run only in real mode
                             * looking for property 'isGRCGame' in 'features' object
                             * checking that it is set to true
                             * @Mikhail Marchanka
                             * As superdragon888 is connected to pre-prod.
                             * I think, @Konstantin, can run games in real mode for demo players.
                             * So GRC tool will be available.
                             * @Konstantin, please note that GRC tool available only for real players (isTest=false) in real mode.
                             * (so we can use it only in pre-prod).
                             *
                             * $gameMode = 'real';
                             */
                            'is_grc_game' => ( property_exists( $games[0], 'features' ) &&
                                               is_object( $games[0]->features ) &&
                                               property_exists( $games[0]->features, 'isGRCGame' ) &&
                                               is_bool( $games[0]->features->isGRCGame ) &&
                                               $games[0]->features->isGRCGame === true ),

                            'game_data' => $game,
                            'entity'    => $entity
                        ];
                    }
                }
            }
        }

        return $activeGames;
    }

    public static function cronProviderGames( Logger $logger ): void {

        $logger->info( 'Started load games from provides. ', [] );

        $self = self::instance();

        $regionService = RegionService::instance();
        $localeService = LocaleService::instance();

        $projectService = ProjectService::instance();

        $dbService = DBService::instance();

        $regions   = $regionService->getAvailableRegions();
        $languages = $localeService->getAvailableLanguages();
        $logger->info( 'Founded active regions: ', array_keys( $regions ) );

        $projectConfig = $projectService::getConfig();
        putenv( $projectConfig['cdn']['google']['cloudStorage']['credentials'] );
        set_time_limit( 300 );

        $sanityGames = $dbService->findItems( Game::_type );
        $gameIds     = [];
        foreach ( $sanityGames as $game ) {
            if ( isset( $game->data['gameReleaseDate'] ) ) {
                var_export( $game->data );
                die();
            }
            $game = new Game( $game->data );
            if ( ! is_array( $game->gameCode ) || count( $game->gameCode ) === 0 ) {
                continue;
            }
            foreach ( $game->gameCode as $gameCode ) {
                $gameCode = new GameCode ( $gameCode->item );
                $gameId   = $game->getGameId();

                $gameIds['id'][ $game->getGameId() ][ $gameCode->gameCode ] = $gameCode->gameCode;
                $gameIds['code'][ $gameCode->gameCode ][ $gameId ]          = $gameId;
            }
        }

        $googleClient = new GoogleCloudStorageService( [
            'projectId' => $projectConfig['cdn']['google']['cloudStorage']['projectId']
        ] );

        $awsClient = AWSStorageService::getDefaultClient();

        $buckets = $projectService::getCdnBuckets();

        foreach ( $buckets as $bucketInfo ) {
            $regionCode = $bucketInfo->bucketRegion;

            // foreach ( $availableLanguages as $languageCode => $language ) {

            $logger->info( 'Start connecting to game api to update games. ', [ 'region' => $regionCode ] );
            $startTime    = microtime( true );
            $games        = $self->getProviderGames( $regionCode );
            $execGameTime = microtime( true ) - $startTime;

            if ( is_array( $games ) ) {
                $logger->info( 'Looking for games. ', [
                    'games'    => count( $games ),
                    'execTime' => number_format( $execGameTime, 4 ),
                    'region'   => $regionCode
                ] );
                $dbService->updateProviderGames( $games, $regionCode );

                foreach ( $languages as $language ) {
                    $json = [];
                    foreach ( $games as $game ) {
                        $gameCode = $game['game_code'];
                        if ( isset( $gameIds['code'][ $gameCode ] ) ) {
                            foreach ( $gameIds['code'][ $gameCode ] as $gameId ) {
                                if ( isset( $gameIds['id'][ $gameId ] ) ) {
                                    $json['id2Code'][ $gameId ] = array_keys( $gameIds['id'][ $gameId ] );
                                }
                            }
                        }
                        $json['freePlay'][ $gameCode ]['play'] =
                            $projectService::generatePlayGameIframeUrl( $gameCode, $language['languageCode'], $regionCode );
                    }
                    if ( ! is_dir( PROJECT_GAMES_PATH ) ) {
                        if ( ! mkdir( $concurrentDirectory = PROJECT_GAMES_PATH, 0755, true ) && ! is_dir( $concurrentDirectory ) ) {
                            throw new RuntimeException( sprintf( 'Directory "%s" was not created', $concurrentDirectory ) );
                        }
                    }
                    $fileName   = 'games-' . $regionCode . '-' . $language['languageCode'] . '.json';
                    $filePath   = PROJECT_GAMES_PATH . D_S . $fileName;
                    $objectName = substr( GAMES_URL, 1 ) . $fileName;
                    try {
                        file_put_contents( $filePath, Json::encode( $json ) );
                    } catch ( JsonException $e ) {
                        $logger->error( (string) $e );
                    }

                    unset( $json );

                    // $googleClient->deleteBucketObject( $bucketInfo['bucketName'], $objectName );
                    if ( PROJECT_ENV === EnumProjectEnv::prod ) {
                        if ( $bucketInfo->type === EnumCdn::google ) {
                            $googleClient->uploadBucketObject( $bucketInfo->bucketName, $objectName, $filePath );
                        }
                        if ( $bucketInfo->type === EnumCdn::amazon ) {
                            $awsClient->uploadBucketObject( $bucketInfo->bucketName, $objectName, $filePath );
                        }
                        $logger->info( 'Uploaded games ' . $objectName );
                    }
                }
            } else {
                $logger->info( 'Can`t retrieve list of games from api. ', [
                    'region'        => $regionCode,
                    'execTime'      => number_format( $execGameTime, 4 ),
                    'error-code'    => \GpApi\Request::getResponseErrorCode(),
                    'error-message' => \GpApi\Request::getResponseErrorMessage()
                ] );
            }
            unset( $games );

            $logger->info( 'Finished uploading games. ', [] );

            SyncService::cdnInvalidateCache( $bucketInfo->type );
        }


        unset( $sanityGames, $gameIds );

        $logger->info( 'Finished downloading games from providers.: ', array_keys( $regions ) );
    }

    /**
     * @param Logger $logger
     */
    public static function cronGameInfoAndMarketingMaterials( Logger $logger ): void {

        $logger->info( 'Started load game info and marketing materials. ', [] );

        $project   = ProjectService::instance();
        $dbService = DBService::instance();

        $projectConfig = $project::getConfig();
        set_time_limit( 3600 );

        $googleClient = new GoogleCloudStorageService( [
            'projectId' => $projectConfig['cdn']['google']['reportStorage']['projectId']
        ] );

        $bucketName = $projectConfig['cdn']['google']['reportStorage']['buckets']['game-stats']['bucketName'];

        $filePath = PROJECT_RUNTIME_PATH . D_S . 'game-info.csv';

        $objects = $googleClient->getBucketObjects( $bucketName, 'eu_prod_games_stats.csv' );

        /** @var StorageObject $object */
        foreach ( $objects as $object ) {
            try {
                $object->downloadToFile( $filePath );
            } catch ( Exception $exception ) {
                $logger->error( (string) $exception );
            }
        }

        $logger->info( 'Started update game info and marketing materials. ', [] );

        if ( file_exists( $filePath ) ) {
            $dbService::updateGameInfoAndMarketingMaterials( $filePath, $logger );
        } else {
            $logger->error( 'File Not Found: ' . $filePath );
        }
        $logger->info( 'Finished update game info and marketing materials. ', [] );

    }

    public function decodeGameData( string $gameString, $saveError = true ): ?GamePlayModel {

        $projectService = ProjectService::instance();

        $crypto = new Crypto();

        $gameData = null;

        try {
            $gameData = $crypto->decrypt( $gameString );
            $gameData = new GamePlayModel( Json::decode( $gameData, true ) );
        } catch ( Throwable $exception ) {
            if ( $saveError ) {
                $errorMessage = 'Unknown game. ' . $exception->getMessage();

                $projectService->getLogger()->addError(
                    'GameCodeEncrypted: [' . $gameString . ' ]. GameCodeDecrypted: [' . var_export( $gameData, true ) . ' ]. Message: [' . $errorMessage . '].'
                );
            }
        }

        return $gameData;
    }

    public static function encodeGamePlayData( GamePlayModel $gamePlayModel ): ?string {

        $self   = self::instance();
        $crypto = new Crypto();

        try {
            return $crypto->encrypt(
                Json::encode( [
                    'gameId'       => $gamePlayModel->gameId,
                    'regionCode'   => $gamePlayModel->regionCode,
                    'languageCode' => $gamePlayModel->languageCode
                ] ) );
        } catch ( JsonException $e ) {
            $self->getLogger()->error( (string) $e );
        }

        return null;
    }

    /**
     * @param $gameCode string
     * @param $gameMode string "fun" or "real"
     * @param $languageCode
     *
     * @return mixed
     */
    public function runGame( $gameCode, $gameMode, $languageCode, Request $request, Response $response, $return = false ): string {

        $self = self::instance();

        $region = RegionService::instance();

        $currentRegionCode = $region->getCurrentRegionCode();
        $availableRegions  = $region->getAvailableRegions();

        $params = $request->getQueryParams();

        $isParams = ( isset( $params['language'] ) && isset( $params['country'] ) && isset( $params['currency'] ) );

        $gameUrl = null;

        $games = $self->getProviderGames( $currentRegionCode, 1000, $gameCode );
        if ( ! isset( $games[ $gameCode ] ) ) {
            $games = $self->getProviderGames( $currentRegionCode === 'eu'
                ? $availableRegions['asia']['regionCode']
                : $availableRegions['eu']['regionCode'] );
            if ( isset( $games[ $gameCode ] ) ) {
                try {
                    $games = [ $games[ $gameCode ] ];
                } catch ( Throwable $exception ) {
                    $games = false;
                    $self->logger->addError(
                        'RunGame: [' . $gameCode . ']. Language: [' . $languageCode . ']. ' .
                        'Message: [' . $exception->getMessage() . ']. ' .
                        'File: [' . $exception->getFile() . ']. ' .
                        'Line: [' . $exception->getLine() . '].'
                    );
                }
            } else {
                $games = false;
            }

        } else {

            try {
                $games = [ $games[ $gameCode ] ];
            } catch ( Throwable $exception ) {
                $games = false;
                $self->logger->addError(
                    'RunGame: [' . $gameCode . ']. Language: [' . $languageCode . ']. ' .
                    'Message: [' . $exception->getMessage() . ']. ' .
                    'File: [' . $exception->getFile() . ']. ' .
                    'Line: [' . $exception->getLine() . '].'
                );
            }
        }

        if ( $games !== false ) {

            if ( is_array( $games ) && count( $games ) === 1 && isset( $games[0]['entity'] ) ) {
                $entity      = $games[0]['entity'];
                $credentials = new SiteCredentials(
                    $entity['site-api']['url'],
                    $entity['player-api']['url'],
                    $entity['token']['value']
                );
                $self->userProvider( $currentRegionCode, $credentials );
            }
            if ( is_array( $games ) && count( $games ) === 1 && isset( $games[0] ) && $games[0]['game_data'] instanceof GameInfoModel ) {

                /**
                 * GRC Games should be run only in real mode
                 * looking for property 'isGRCGame' in 'features' object
                 * checking that it is set to true
                 * @Mikhail Marchanka
                 * As superdragon888 is connected to pre-prod.
                 * I think, @Konstantin, can run games in real mode for demo players.
                 * So GRC tool will be available.
                 * @Konstantin, please note that GRC tool available only for real players (isTest=false) in real mode.
                 * (so we can use it only in pre-prod).
                 */
//				if ( property_exists( $games[0], 'features' ) &&
//				     is_object( $games[0]->features ) &&
//				     property_exists( $games[0]->features, 'isGRCGame' ) &&
//				     is_bool( $games[0]->features->isGRCGame ) &&
//				     $games[0]->features->isGRCGame === true
//				) {
//					$gameMode = 'real';
//				}

                if ( $gameMode === 'fun' ) {
                    // First, try to get game URL from provider API
                    /** @var GameInfoModel $gameInfo */
                    // $gameInfo = $games[0];
                    /** @var GameURLInfoModel $gameUrlData */
                    $gameUrlData = Site::getGameFunModeUrl( $gameCode );

                    if ( $gameUrlData instanceof GameURLInfoModel && ! empty( $gameUrlData->url ) ) {
                        // Provider API returned a valid URL
                        $gameUrl = $gameUrlData->url;
                        $self->logger->addDebug(
                            'Game started using provider API: [' . $gameCode . ']. Language: [' . $languageCode . '].'
                        );
                    }

//
//                            return $response->withStatus( 302 )
//                                            ->withAddedHeader( 'Location', $self->getGameUrl( $gameUrlData->url, $languageCode ) );
//					}
//
//					$message = ( $languageCode === 'zh-cn'
//						? '无法获得游戏网址 "' . $gameCode . '".'
//						: 'It is not possible to obtain game url for "' . $gameCode . '".' );
                } else {
                    $message = ( $languageCode === 'zh-cn'
                        ? '只有授权客户才能玩真钱游戏。'
                        : 'Only authorized customers can play games for real money.' );
                }
            } else {
                $message = ( $languageCode === 'zh-cn'
                    ? '未找到游戏代码"' . $gameCode . '"。'
                    : 'Game code "' . $gameCode . '" not found.' );
            }
        } else {
            $message = ( $languageCode === 'zh-cn'
                    ? '无法获得游戏信息。'
                    : 'It is not possible to obtain game information. ' ) . $gameCode;
        }

        if ( isset( $message ) ) {
            $self->logger->addInfo(
                'RunGame: [' . $gameCode . ']. Language: [' . $languageCode . ']. Message: [' . $message . '].'
            );
        }

        if ( ! empty( $gameUrl ) ) {
            $gameUrl = $self->getGameUrl(
                $gameUrlData->url,
                ( $isParams ? $params['language'] : $languageCode ),
                ( $isParams ? $params['currency'] : null ),
                ( $currentRegionCode !== EnumRegion::eu ) // configuration of European environment doesn't allow to play without game token
            );
            if ( $return === false ) {
                header( 'Location: ' . $gameUrl );
                die();
            } else {
                return $gameUrl;
            }
        }

        return '';
    }

    /**
     * @return array
     */
    public function getGamesPerLayout(): array {

        return self::instance()->gamesPerLayout;
    }

    /**
     * @param string $gameId
     *
     * @param string $currentLanguageCode
     *
     * @return Game
     */
    public static function getGame( string $gameId, string $currentLanguageCode ): ?Game {

        $self = self::instance();

        $project = ProjectService::instance();

        try {
            $game = $project->findItem( Game::_type, $gameId, EnumByParam::code, 'gameId.current' );

            if ( is_array( $game ) ) {

                return new Game( $game );
            }
        } catch ( Exception $exception ) {
            $self->getLogger()->error( (string) $exception );
        }

        return null;
    }

    public static function getGamesPage( string $regionCode, string $languageCode ): ?PageGames {

        $self = self::instance();

        $project = ProjectService::instance();

        try {
            $page      = null;
            $pageGames = $project->findItem( PageGames::_type, 'pageGame-' . $regionCode );
            if ( is_array( $pageGames ) ) {
                $page = new PageGames( $pageGames );
            }

            return $page;
        } catch ( Exception $exception ) {
            $self->getLogger()->error( (string) $exception );
        }

        return null;
    }

    /**
     * @param string $languageCode
     *
     * @return ItemModel
     */
    public static function getGamesForCategories( string $languageCode ): ItemModel {

        $self = self::instance();

        if ( ! $self->games instanceof ItemModel ) {

            $sanity = SanityService::instance();

            $id    = $code = [];
            $games = $sanity::getGamesForGameCategory( $languageCode );
            foreach ( $games as $game ) {
                $id[ $game['_id'] ] = $game;

                $code[ $game['gameId']['current'] ] = $game['_id'];
            }

            $self->games = new ItemModel( $id, $code );
        }

        return $self->games;
    }

    public static function getGameCategories( string $regionCode, string $languageCode ): ItemModel {

        $self = self::instance();

        $id        = $code = [];
        $pageGames = $self::getGamesPage( $regionCode, $languageCode );
        if ( $pageGames instanceof PageGames ) {

            if ( is_array( $pageGames->gamesCategories ) ) {

                foreach ( $pageGames->gamesCategories as $category ) {
                    if ( $category->isActive === true ) {
                        $return[] = $category;
                    }
                    $id[ $category->_id ] = $category;

                    $code[ $category->categoryCode->current ] = $category->_id;
                }
            }
        }
        $self->gameCategories = new ItemModel( $id, $code );

        return $self->gameCategories;
    }

    /**
     * @param string $gameId
     *
     * @return string|null
     * @throws HttpNotFoundException
     * @throws Throwable
     */
    public static function buildGamePage( string $gameId ): ?string {

        $self = self::instance();

        $project = ProjectService::instance();

        $region = RegionService::instance();

        $localeService = LocaleService::instance();

        $currentLanguageCode = $localeService->getCurrentLanguageCode();

        $currentRegionCode = $region->getCurrentRegionCode();
        $game              = $self::getGame( $gameId, $currentLanguageCode );

        if ( $game instanceof Game ) {

            return $project->getRenderer()->fetch( 'about.phtml', new ViewPageGameModel( [
                'game'                => $game,
                'currentLanguageCode' => $currentLanguageCode,
                'currentRegionCode'   => $currentRegionCode,
                'common'              => $localeService->getTranslation( 'common' ),
                'translation'         => $localeService->getTranslation( 'page-game' ),
            ] ) );
        }
        throw new HttpNotFoundException( $project->getRequest() );
    }

    /**
     * @param GamePlaylist[] $array
     *
     * @return array
     */
    public function getActivePlaylists( array $array ): array {

        $activePlaylists = [];
        if ( is_array( $array ) && count( $array ) > 0 ) {
            foreach ( $array as $pi => $playlist ) {

                if ( $playlist->isActive === true ) {

                    if ( is_array( $playlist->playlistGames ) && count( $playlist->playlistGames ) > 0 ) {
                        foreach ( $playlist->playlistGames as $gi => $gameLine ) {
                            if ( $gameLine instanceof GameLine &&
                                 $gameLine->game instanceof SanityReference &&
                                 is_array( $gameLine->game->item )
                            ) {
                                $game = new Game( $gameLine->game->item );
                                if ( $gameLine->gameRibbon instanceof SanityReference ) {
                                    $game->gameRibbon = $gameLine->gameRibbon;
                                }

                                $playlist->playlistGames[ $gi ] = $game;
                            }
                        }
                    }

                    $activePlaylists[] = $playlist;
                }
            }
        }

        return $activePlaylists;
    }

    /**
     * @param string $playlistCode
     *
     * @return string|null
     * @throws HttpNotFoundException
     * @throws Throwable
     */
    public static function buildPlaylistPage( string $playlistCode ): ?string {

        $self          = self::instance();
        $project       = ProjectService::instance();
        $region        = RegionService::instance();
        $localeService = LocaleService::instance();

        $currentLanguageCode = $localeService->getCurrentLanguageCode();

        $currentRegionCode = $region->getCurrentRegionCode();

        $page = $self::getGamesPage( $currentRegionCode, $currentLanguageCode );

        /** @var GamePlaylist[] $activePlaylists */
        $activePlaylists = ( $self->getActivePlaylists(
            $page->gamePlaylist ?? []
        ) );

        /** @var GamePlaylist|null $currentPlaylist */
        $currentPlaylist = null;

        foreach ( $activePlaylists as $playlist ) {

            if ( $playlist->playlistCode->current === $playlistCode ) {
                $currentPlaylist = $playlist;
            }
        }

        if ( $currentPlaylist instanceof GamePlaylist ) {

            return $project->getRenderer()->fetch( 'playlistpage.phtml', new ViewPagePlaylistModel( [
                'page'                => $page,
                'activePlaylists'     => $activePlaylists,
                'currentPlaylist'     => $currentPlaylist,
                'currentLanguageCode' => $currentLanguageCode,
                'currentRegionCode'   => $currentRegionCode,
                'common'              => $localeService->getTranslation( 'common' ),
                'translation'         => $localeService->getTranslation( 'page-playlist' ),
            ] ) );
        }
        throw new HttpNotFoundException( $project->getRequest() );
    }


    /**
     * @param string $categoryCode
     *
     * @return string
     * @throws HttpNotFoundException
     * @throws Throwable
     */
    public static function buildGamesPage2(  ): string {

        $self          = self::instance();
        $project       = ProjectService::instance();
        $regionService = RegionService::instance();
        $localeService = LocaleService::instance();
        $languageCode  = $localeService->getCurrentLanguageCode();
        $regionCode    = $regionService->getCurrentRegionCode();

        $gamesPage = $self::getGamesPage( $regionCode, $languageCode );


        return $project->getRenderer()->fetch( 'games.phtml', new ViewPageGames2Model( [
            'page'                => $gamesPage,
            'currentLanguageCode' => $languageCode,
            'currentRegionCode'   => $regionCode,
            'common'              => $localeService->getTranslation( 'common' ),
            'translation'         => $localeService->getTranslation( 'page-games' ),
        ] ) );
    }

    /**
     * @param string $categoryCode
     *
     * @return string
     * @throws HttpNotFoundException
     * @throws Throwable
     */
    public static function buildGamesPage( string $categoryCode ): string {

        $self          = self::instance();
        $project       = ProjectService::instance();
        $regionService = RegionService::instance();
        $localeService = LocaleService::instance();
        $languageCode  = $localeService->getCurrentLanguageCode();
        $regionCode    = $regionService->getCurrentRegionCode();

        $gamesPage = $self::getGamesPage( $regionCode, $languageCode );

        $gameCategories = $self::getGameCategories( $regionCode, $languageCode );

        /** @var GameCategory $currentGameCategory */
        $categoryGames = [];

        if ( $gameCategories instanceof ItemModel ) {

            $trendingCategory = null;

            /** @var GameCategory $currentGameCategory */
            foreach ( $gameCategories->getId() as $currentGameCategory ) {

                if ( $currentGameCategory->categoryCode->current === 'trending-asia' ) {
                    $trendingCategory = $currentGameCategory;
                }

                $gamesPerSlide = ( in_array( $currentGameCategory->categoryType, [
                    GameCategoryTypes::arcade,
                    GameCategoryTypes::upcoming
                ] ) ? 1 : 3 );
                $gamesInSlide  = 0;
                $slide         = 0;

                if ( is_array( $currentGameCategory->categoryGames ) ) {

                    foreach ( $currentGameCategory->categoryGames as $layout ) {

                        $gamesPerLayout = ( in_array( $currentGameCategory->categoryType, [
                            GameCategoryTypes::arcade,
                            GameCategoryTypes::upcoming
                        ] )
                            ? 1
                            : $self->gamesPerLayout[ $layout->layout ] );

                        $gamesInSlide += $gamesPerLayout;

                        if ( $gamesInSlide > $gamesPerSlide ) {
                            $gamesInSlide = $gamesPerLayout;
                            $slide ++;
                        }

                        if ( $layout->game instanceof SanityReference && is_array( $layout->game->item ) ) {
                            $game               = new Game( $layout->game->item );
                            $game->gameLayout   = $layout->layout;
                            $game->gameCategory = $currentGameCategory;

                            if ( $layout->gameRibbon instanceof SanityReference ) {
                                $game->gameRibbon = $layout->gameRibbon;
                            }

                            $categoryGames[ $currentGameCategory->categoryCode->current ][ $slide ]['games'][] = $game;
                        }
                    }
                }
            }

            if ( $regionCode === EnumRegion::asiaskywind && $trendingCategory ) {
                $countries = $project->getCountries();
                foreach ( $countries as $country ) {
                    $gamesPerSlide = 3;
                    $gamesInSlide  = 0;
                    $slide         = 0;
                    foreach ( $country->games as $gameLine ) {
                        $gamesPerLayout = $self->gamesPerLayout[ $gameLine->layout ];

                        $gamesInSlide += $gamesPerLayout;

                        if ( $gamesInSlide > $gamesPerSlide ) {
                            $gamesInSlide = $gamesPerLayout;
                            $slide ++;
                        }

                        if ( $gameLine->game instanceof SanityReference && is_array( $gameLine->game->item ) ) {

                            $game               = new Game( $gameLine->game->item );
                            $game->gameLayout   = $gameLine->layout;
                            $game->gameCategory = $trendingCategory;

                            if ( $gameLine->gameRibbon instanceof SanityReference ) {
                                $game->gameRibbon = $layout->gameRibbon;
                            }

                            if ( $game instanceof Game ) {
                                $categoryGames[ 'trending-asia-' . strtolower( $country->countryCode ) ][ $slide ]['games'][] = $game;
                            }
                        }
                    }
                }
            }
        }


        $brandedGames = [];
        if ( ( $gamesPage !== null ) && is_array( $gamesPage->gamesBranded ) ) {
            foreach ( $gamesPage->gamesBranded as $gameLine ) {
                if ( $gameLine instanceof GameLine &&
                     $gameLine->game instanceof SanityReference &&
                     is_array( $gameLine->game->item )
                ) {
                    $game = new Game( $gameLine->game->item );
                    if ( $gameLine->gameRibbon instanceof SanityReference ) {
                        $game->gameRibbon = $gameLine->gameRibbon;
                    }

                    $brandedGames[] = $game;
                }
            }
        }


        /** @var GamePlaylist[] $activePlaylists */
        $activePlaylists = ( $self->getActivePlaylists(
            $gamesPage->gamePlaylist ?? []
        ) );

        if ( is_array( $categoryGames ) ) {
            return $project->getRenderer()->fetch( 'catalog.phtml', new ViewPageGamesModel( [
                'page'                => $gamesPage,
                'gameCategories'      => $gameCategories,
                'activePlaylists'     => $activePlaylists,
                'brandedGames'        => $brandedGames,
                'categoryGames'       => $categoryGames,
                'currentCategory'     => $categoryCode,
                'currentLanguageCode' => $languageCode,
                'currentRegionCode'   => $regionCode,
                'common'              => $localeService->getTranslation( 'common' ),
                'translation'         => $localeService->getTranslation( 'page-games' ),
            ] ) );
        }
        throw new HttpNotFoundException( $project->getRequest() );
    }

    /**
     * @param string $regionCode
     * @param string $languageCode
     *
     * @return PageLiveCasino|null
     */
    public
    static function getLiveCasinoPage(
        string $regionCode, string $languageCode
    ): ?PageLiveCasino {

        $self = self::instance();

        $project = ProjectService::instance();

        try {
            $page           = null;
            $pageLiveCasino = $project->findItem( PageLiveCasino::_type, PageLiveCasino::_type . '-' . $regionCode );
            if ( is_array( $pageLiveCasino ) ) {
                $page = new PageLiveCasino( $pageLiveCasino );
            }

            return $page;
        } catch ( Exception $exception ) {
            $self->getLogger()->error( (string) $exception );
        }

        return null;
    }

    /**
     * @return string
     * @throws Throwable
     */
    public
    static function buildLiveCasinoPage(): string {

        $self = self::instance();

        $project = ProjectService::instance();

        $region = RegionService::instance();

        $localeService = LocaleService::instance();

        $currentLanguageCode = $localeService->getCurrentLanguageCode();

        $currentRegionCode = $region->getCurrentRegionCode();

        $page = $self::getLiveCasinoPage( $currentRegionCode, $currentLanguageCode );
        if ( ! $page instanceof PageLiveCasino ) {
            throw new HttpNotFoundException( $project->getRequest() );
        }

        return $project->getRenderer()->fetch( 'live-casino.phtml', new ViewPageLiveCasino( [
            'page'                => $page,
            'currentLanguageCode' => $currentLanguageCode,
            'currentRegionCode'   => $currentRegionCode,
            'common'              => $localeService->getTranslation( 'common' ),
            'translation'         => $localeService->getTranslation( 'page-live-casino' ),
        ] ) );
    }

    /**
     * @param string $currentLanguageCode
     *
     * @throws Exception
     */
    public
    function setCurrentLanguageCode(
        string $currentLanguageCode
    ): void {

        $self = self::instance();
        if ( $self->currentLanguageCode !== null && $currentLanguageCode === $self->currentLanguageCode ) {
            return;
        }

        $project = ProjectService::instance();

        $newLanguageCode = $self->getDefaultLanguageCode();
        $languages       = $project->getLanguages();
        foreach ( $languages as $language ) {
            if ( $language->languageCode === $currentLanguageCode ) {
                $_SESSION[ $self->sessionLanguageParam ] = $newLanguageCode = $language->languageCode;
                break;
            }
        }
        if ( $newLanguageCode === null ) {
            throw new Exception( 'Provided new language code was not found in the available active project languages.' );
        }

        $self->currentRegionCode = $currentLanguageCode;
    }

    /**
     * @return string
     * @throws Exception
     */
    public
    function getCurrentLanguageCode(): string {

        $self = self::instance();

        if ( isset( $_SESSION[ $self->sessionLanguageParam ] ) && $self->isLanguageCodeExists( $_SESSION[ $self->sessionLanguageParam ] ) ) {
            $self->currentRegionCode = $_SESSION[ $self->sessionLanguageParam ];

            return $_SESSION[ $self->sessionLanguageParam ];
        }

        return ( $self->currentRegionCode ?? $self->getDefaultLanguageCode() );
    }

    /**
     * @return string
     * @throws Exception
     */
    public
    function getDefaultLanguageCode(): string {

        $self = self::instance();

        $self->defaultLanguageCode = 'default';

        return $self->defaultLanguageCode;
    }

    /**
     * @return string
     */
    public
    function getSessionLanguageParam(): string {
        return $this->sessionLanguageParam;
    }

    /**
     * @param string $languageCode
     *
     * @return bool
     */
    public
    function isLanguageCodeExists(
        string $languageCode
    ): bool {

        $project = ProjectService::instance();

        $languages = $project->getLanguages();
        foreach ( $languages as $language ) {
            if ( $language->languageCode === $languageCode ) {
                return true;
            }
        }

        return false;
    }

}

GameService::instance();